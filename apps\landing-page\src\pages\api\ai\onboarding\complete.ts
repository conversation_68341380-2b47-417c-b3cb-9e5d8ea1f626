import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import { prisma } from '@freela/database';

interface CompleteOnboardingRequest {
  sessionId: string;
  extractedData: any;
}

interface CompleteOnboardingResponse {
  success: boolean;
  message: string;
  data?: {
    userId: string;
    hasCompletedOnboarding: boolean;
    profileCreated: boolean;
    redirectUrl: string;
  };
  error?: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<CompleteOnboardingResponse>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  try {
    // Get user session
    const session = await getServerSession(req, res, authOptions);
    
    if (!session?.user?.id) {
      return res.status(401).json({
        success: false,
        message: 'Unauthorized'
      });
    }

    const { sessionId, extractedData }: CompleteOnboardingRequest = req.body;

    // Validate required fields
    if (!sessionId) {
      return res.status(400).json({
        success: false,
        message: 'Missing required field: sessionId'
      });
    }

    const userId = session.user.id;
    const userRole = session.user.role as 'CLIENT' | 'EXPERT' | 'ADMIN';

    // Update user's onboarding status
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: { hasCompletedOnboarding: true },
      select: {
        id: true,
        role: true,
        hasCompletedOnboarding: true,
        expertProfile: true,
        clientProfile: true
      }
    });

    let profileCreated = false;
    let redirectUrl = '/';

    // Create appropriate profile based on user role
    if (userRole === 'EXPERT' && !updatedUser.expertProfile) {
      try {
        await createExpertProfile(userId, extractedData);
        profileCreated = true;
        redirectUrl = 'http://localhost:3002/dashboard';
      } catch (error) {
        console.error('Failed to create expert profile:', error);
        // Continue without failing the onboarding
      }
    } else if (userRole === 'CLIENT' && !updatedUser.clientProfile) {
      try {
        await createClientProfile(userId, extractedData);
        profileCreated = true;
        redirectUrl = '/?auth=success&role=client&onboarding=complete';
      } catch (error) {
        console.error('Failed to create client profile:', error);
        // Continue without failing the onboarding
      }
    } else if (userRole === 'ADMIN') {
      redirectUrl = 'http://localhost:3001/dashboard';
    }

    // Try to notify the main API about completion (optional)
    try {
      await fetch(`${process.env.API_BASE_URL || 'http://localhost:3001'}/api/ai/onboarding/complete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${userId}`,
        },
        body: JSON.stringify({
          sessionId,
          extractedData,
          userId,
          userRole
        }),
      });
    } catch (error) {
      console.error('Failed to notify main API about onboarding completion:', error);
      // Don't fail the request if this fails
    }

    return res.status(200).json({
      success: true,
      message: 'Onboarding completed successfully',
      data: {
        userId,
        hasCompletedOnboarding: true,
        profileCreated,
        redirectUrl
      }
    });

  } catch (error: any) {
    console.error('Error completing onboarding:', error);
    
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
}

/**
 * Create expert profile with extracted data
 */
async function createExpertProfile(userId: string, extractedData: any) {
  const profileData = {
    userId,
    title: extractedData?.title || { ar: 'خبير محترف', en: 'Professional Expert' },
    description: extractedData?.description || { ar: 'خبير محترف في مجاله', en: 'Professional expert in their field' },
    skills: extractedData?.skills || [],
    experience: extractedData?.experience || 'INTERMEDIATE',
    hourlyRate: extractedData?.hourlyRate || null,
    availability: extractedData?.availability || {
      hoursPerWeek: 40,
      timezone: 'Asia/Damascus',
      workingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'sunday']
    },
    responseTime: extractedData?.responseTime || 'WITHIN_24_HOURS',
  };

  return await prisma.expertProfile.create({
    data: profileData
  });
}

/**
 * Create client profile with extracted data
 */
async function createClientProfile(userId: string, extractedData: any) {
  const profileData = {
    userId,
    companyName: extractedData?.companyName || null,
    companySize: extractedData?.companySize || null,
    industry: extractedData?.industry || null,
  };

  return await prisma.clientProfile.create({
    data: profileData
  });
}
