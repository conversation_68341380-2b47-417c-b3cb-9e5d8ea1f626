# AI Onboarding System Test Plan

## Current Status ✅
- **API Server**: Running on port 3001 with mock endpoints
- **Landing Page**: Running on port 3004
- **Google OAuth**: Configured and working
- **Components**: All AI onboarding components exist

## Test Steps

### 1. Authentication Flow Test
1. Go to http://localhost:3004
2. Click "تسجيل الدخول" (Sign In)
3. Choose Google OAuth
4. Complete authentication
5. **Expected**: Redirect to `/ai-onboarding` page

### 2. Role Selection Test
1. After authentication, should see role selection interface
2. **Expected**: Three role cards (Expert, Client, Business)
3. Click on "أنا خبير" (I'm an Expert) or "أنا عميل" (I'm a Client)
4. **Expected**: Progress to data collection form

### 3. Data Collection Form Test
1. Fill out the form with:
   - First Name: أحمد
   - Last Name: محمد
   - Email: (auto-filled from Google)
   - Phone: +963912345678
   - Location: دمشق، ريف دمشق
   - Service preferences (for experts) or project types (for clients)
2. Click "متابعة" (Continue)
3. **Expected**: Progress to AI introduction

### 4. AI Introduction Test
1. Should see AI introduction screen with role-specific content
2. Click "بدء المحادثة" (Start Conversation)
3. **Expected**: Progress to chat interface

### 5. Chat Interface Test
1. Should see chat interface with welcome message
2. Type a message: "مرحبا، أنا مطور ويب"
3. Send message
4. **Expected**: AI responds with relevant question
5. Continue conversation for 3-5 messages
6. **Expected**: AI completes conversation and shows completion

### 6. Completion Celebration Test
1. After AI conversation completion
2. **Expected**: See celebration screen with confetti
3. **Expected**: Automatic redirect to appropriate dashboard after 3 seconds

## API Endpoints Fixed ✅
- `/api/ai/conversation/start` → `/api/v1/ai/conversation/start`
- `/api/ai/conversation/message` → `/api/v1/ai/conversation/message`
- `/api/onboarding/user-data` → Added to simple server
- All endpoints now point to localhost:3001

## Mock Data Implementation ✅
- Welcome messages in Arabic for both Expert and Client roles
- Random AI responses for conversation
- Completion detection after 3-5 messages
- Mock extracted data (skills, experience, budget, location)

## Known Issues Fixed ✅
1. **API URL Mismatch**: Fixed endpoint URLs
2. **Missing Endpoints**: Added missing endpoints to simple server
3. **Completion Logic**: Added completion detection
4. **Data Persistence**: Added mock data save endpoint

## Next Steps for Testing
1. Open browser to http://localhost:3004
2. Complete full authentication flow
3. Test each onboarding step
4. Verify AI conversation works
5. Confirm completion and redirect

## Debugging Tips
- Check browser console for any JavaScript errors
- Check API server logs in terminal
- Verify network requests in browser dev tools
- Test each component individually if issues persist
