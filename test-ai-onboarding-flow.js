// AI Onboarding Flow Test Script
// Run this in the browser console to test the flow

console.log('🚀 Starting AI Onboarding Flow Test...');

// Test 1: Check if API server is running
async function testAPIServer() {
  console.log('\n📡 Testing API Server...');
  try {
    const response = await fetch('http://localhost:3001/health');
    const data = await response.json();
    console.log('✅ API Server Status:', data.status);
    return true;
  } catch (error) {
    console.error('❌ API Server Error:', error);
    return false;
  }
}

// Test 2: Test AI conversation start endpoint
async function testAIConversationStart() {
  console.log('\n🤖 Testing AI Conversation Start...');
  try {
    const response = await fetch('http://localhost:3001/api/v1/ai/conversation/start', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userRole: 'EXPERT',
        language: 'ar',
        sessionType: 'onboarding'
      })
    });
    
    const data = await response.json();
    console.log('✅ AI Conversation Start Response:', data);
    return data.success ? data.data.sessionId : null;
  } catch (error) {
    console.error('❌ AI Conversation Start Error:', error);
    return null;
  }
}

// Test 3: Test AI message endpoint
async function testAIMessage(sessionId) {
  console.log('\n💬 Testing AI Message...');
  try {
    const response = await fetch('http://localhost:3001/api/v1/ai/conversation/message', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sessionId: sessionId,
        message: 'مرحبا، أنا مطور ويب محترف',
        messageType: 'text'
      })
    });
    
    const data = await response.json();
    console.log('✅ AI Message Response:', data);
    return data.success;
  } catch (error) {
    console.error('❌ AI Message Error:', error);
    return false;
  }
}

// Test 4: Test onboarding data save endpoint
async function testOnboardingDataSave() {
  console.log('\n💾 Testing Onboarding Data Save...');
  try {
    const response = await fetch('http://localhost:3001/api/onboarding/user-data', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId: 'test-user-123',
        firstName: 'أحمد',
        lastName: 'محمد',
        email: '<EMAIL>',
        phoneNumber: '+963 91 234 5678',
        location: {
          governorate: 'دمشق',
          city: 'دمشق'
        },
        role: 'EXPERT',
        servicePreferences: ['تطوير المواقع', 'البرمجة']
      })
    });
    
    const data = await response.json();
    console.log('✅ Onboarding Data Save Response:', data);
    return data.success;
  } catch (error) {
    console.error('❌ Onboarding Data Save Error:', error);
    return false;
  }
}

// Test 5: Test onboarding completion endpoint
async function testOnboardingCompletion() {
  console.log('\n🎉 Testing Onboarding Completion...');
  try {
    const response = await fetch('http://localhost:3001/api/ai/onboarding/complete', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sessionId: 'test-session-123',
        extractedData: {
          skills: ['تطوير الويب', 'البرمجة'],
          experience: 'متوسط',
          budget: 'متوسط'
        }
      })
    });
    
    const data = await response.json();
    console.log('✅ Onboarding Completion Response:', data);
    return data.success;
  } catch (error) {
    console.error('❌ Onboarding Completion Error:', error);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  console.log('🧪 Running All AI Onboarding Tests...\n');
  
  const results = {
    apiServer: await testAPIServer(),
    aiConversationStart: null,
    aiMessage: false,
    onboardingDataSave: await testOnboardingDataSave(),
    onboardingCompletion: await testOnboardingCompletion()
  };
  
  // Test AI conversation flow
  const sessionId = await testAIConversationStart();
  results.aiConversationStart = !!sessionId;
  
  if (sessionId) {
    results.aiMessage = await testAIMessage(sessionId);
  }
  
  // Summary
  console.log('\n📊 Test Results Summary:');
  console.log('='.repeat(50));
  Object.entries(results).forEach(([test, result]) => {
    const status = result ? '✅ PASS' : '❌ FAIL';
    console.log(`${test.padEnd(20)}: ${status}`);
  });
  
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log('='.repeat(50));
  console.log(`Overall: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! AI Onboarding system is ready.');
  } else {
    console.log('⚠️ Some tests failed. Check the errors above.');
  }
  
  return results;
}

// Auto-run tests
runAllTests();
