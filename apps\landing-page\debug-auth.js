/**
 * Debug Authentication Flow
 * Simple test script to verify NextAuth configuration
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 FREELA SYRIA AUTHENTICATION DEBUG');
console.log('=====================================\n');

// Check environment variables
console.log('1. Environment Variables Check:');
const envPath = path.join(__dirname, '.env.local');
if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  const lines = envContent.split('\n').filter(line => line.trim() && !line.startsWith('#'));
  
  const requiredVars = [
    'NEXTAUTH_URL',
    'NEXTAUTH_SECRET',
    'GOOGLE_CLIENT_ID',
    'GOOGLE_CLIENT_SECRET'
  ];
  
  requiredVars.forEach(varName => {
    const found = lines.find(line => line.startsWith(`${varName}=`));
    if (found) {
      const value = found.split('=')[1];
      console.log(`   ✅ ${varName}: ${value.substring(0, 20)}...`);
    } else {
      console.log(`   ❌ ${varName}: MISSING`);
    }
  });
} else {
  console.log('   ❌ .env.local file not found');
}

console.log('\n2. NextAuth Configuration Check:');
const authConfigPath = path.join(__dirname, 'src/pages/api/auth/[...nextauth].ts');
if (fs.existsSync(authConfigPath)) {
  console.log('   ✅ NextAuth config file exists');
  
  const configContent = fs.readFileSync(authConfigPath, 'utf8');
  
  // Check for key components
  const checks = [
    { name: 'GoogleProvider', pattern: /GoogleProvider\s*\(/ },
    { name: 'redirect callback', pattern: /async redirect\s*\(/ },
    { name: 'jwt callback', pattern: /async jwt\s*\(/ },
    { name: 'session callback', pattern: /async session\s*\(/ },
    { name: 'signIn callback', pattern: /async signIn\s*\(/ }
  ];
  
  checks.forEach(check => {
    if (check.pattern.test(configContent)) {
      console.log(`   ✅ ${check.name} configured`);
    } else {
      console.log(`   ❌ ${check.name} missing`);
    }
  });
} else {
  console.log('   ❌ NextAuth config file not found');
}

console.log('\n3. AI Onboarding Page Check:');
const onboardingPath = path.join(__dirname, 'src/pages/ai-onboarding.tsx');
if (fs.existsSync(onboardingPath)) {
  console.log('   ✅ AI onboarding page exists');
  
  const onboardingContent = fs.readFileSync(onboardingPath, 'utf8');
  
  // Check for key components
  const onboardingChecks = [
    { name: 'useSession hook', pattern: /useSession/ },
    { name: 'authentication check', pattern: /status === 'unauthenticated'/ },
    { name: 'onboarding completion check', pattern: /hasCompletedOnboarding/ },
    { name: 'role selection', pattern: /RoleSelection/ }
  ];
  
  onboardingChecks.forEach(check => {
    if (check.pattern.test(onboardingContent)) {
      console.log(`   ✅ ${check.name} implemented`);
    } else {
      console.log(`   ❌ ${check.name} missing`);
    }
  });
} else {
  console.log('   ❌ AI onboarding page not found');
}

console.log('\n4. Package Dependencies Check:');
const packagePath = path.join(__dirname, 'package.json');
if (fs.existsSync(packagePath)) {
  const packageContent = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  const deps = { ...packageContent.dependencies, ...packageContent.devDependencies };
  
  const requiredDeps = [
    'next-auth',
    'next',
    'react',
    '@next-auth/prisma-adapter'
  ];
  
  requiredDeps.forEach(dep => {
    if (deps[dep]) {
      console.log(`   ✅ ${dep}: ${deps[dep]}`);
    } else {
      console.log(`   ❌ ${dep}: MISSING`);
    }
  });
} else {
  console.log('   ❌ package.json not found');
}

console.log('\n5. Recommendations:');
console.log('   📝 Test the authentication flow manually:');
console.log('      1. Start the server: npm run dev');
console.log('      2. Open http://localhost:3004');
console.log('      3. Click Google sign-in');
console.log('      4. Check browser console for logs');
console.log('      5. Verify redirect to /ai-onboarding');

console.log('\n   🔧 Debug steps if redirect fails:');
console.log('      1. Check browser Network tab for redirect responses');
console.log('      2. Look for NextAuth logs in server console');
console.log('      3. Verify Google OAuth callback URL configuration');
console.log('      4. Test with different browsers/incognito mode');

console.log('\n✅ Debug script completed!');
