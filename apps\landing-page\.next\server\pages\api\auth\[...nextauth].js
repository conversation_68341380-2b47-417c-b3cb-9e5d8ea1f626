"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/auth/[...nextauth]";
exports.ids = ["pages/api/auth/[...nextauth]"];
exports.modules = {

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/providers/google":
/*!*********************************************!*\
  !*** external "next-auth/providers/google" ***!
  \*********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/google");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/../../node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\auth\\[...nextauth].ts */ \"(api)/./src/pages/api/auth/[...nextauth].ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/auth/[...nextauth]\",\n        pathname: \"/api/auth/[...nextauth]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1yb3V0ZS1sb2FkZXIvaW5kZXguanM/a2luZD1QQUdFU19BUEkmcGFnZT0lMkZhcGklMkZhdXRoJTJGJTVCLi4ubmV4dGF1dGglNUQmcHJlZmVycmVkUmVnaW9uPSZhYnNvbHV0ZVBhZ2VQYXRoPS4lMkZzcmMlNUNwYWdlcyU1Q2FwaSU1Q2F1dGglNUMlNUIuLi5uZXh0YXV0aCU1RC50cyZtaWRkbGV3YXJlQ29uZmlnQmFzZTY0PWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBc0c7QUFDdkM7QUFDTDtBQUMxRDtBQUNzRTtBQUN0RTtBQUNBLGlFQUFlLHdFQUFLLENBQUMsNERBQVEsWUFBWSxFQUFDO0FBQzFDO0FBQ08sZUFBZSx3RUFBSyxDQUFDLDREQUFRO0FBQ3BDO0FBQ08sd0JBQXdCLGdIQUFtQjtBQUNsRDtBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxZQUFZO0FBQ1osQ0FBQzs7QUFFRCIsInNvdXJjZXMiOlsid2VicGFjazovL0BmcmVlbGEvbGFuZGluZy1wYWdlLz9kN2Q4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFBhZ2VzQVBJUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9wYWdlcy1hcGkvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgaG9pc3QgfSBmcm9tIFwibmV4dC9kaXN0L2J1aWxkL3RlbXBsYXRlcy9oZWxwZXJzXCI7XG4vLyBJbXBvcnQgdGhlIHVzZXJsYW5kIGNvZGUuXG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiLi9zcmNcXFxccGFnZXNcXFxcYXBpXFxcXGF1dGhcXFxcWy4uLm5leHRhdXRoXS50c1wiO1xuLy8gUmUtZXhwb3J0IHRoZSBoYW5kbGVyIChzaG91bGQgYmUgdGhlIGRlZmF1bHQgZXhwb3J0KS5cbmV4cG9ydCBkZWZhdWx0IGhvaXN0KHVzZXJsYW5kLCBcImRlZmF1bHRcIik7XG4vLyBSZS1leHBvcnQgY29uZmlnLlxuZXhwb3J0IGNvbnN0IGNvbmZpZyA9IGhvaXN0KHVzZXJsYW5kLCBcImNvbmZpZ1wiKTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IFBhZ2VzQVBJUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLlBBR0VTX0FQSSxcbiAgICAgICAgcGFnZTogXCIvYXBpL2F1dGgvWy4uLm5leHRhdXRoXVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL2F1dGgvWy4uLm5leHRhdXRoXVwiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcIlwiXG4gICAgfSxcbiAgICB1c2VybGFuZFxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBhZ2VzLWFwaS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/auth/[...nextauth].ts":
/*!*********************************************!*\
  !*** ./src/pages/api/auth/[...nextauth].ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"next-auth\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"next-auth/providers/google\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// import { PrismaAdapter } from '@next-auth/prisma-adapter';\n// import { prisma } from '@freela/database';\n// import { Prisma } from '@prisma/client';\nconst authOptions = {\n    // adapter: PrismaAdapter(prisma), // Temporarily disabled for testing\n    providers: [\n        next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1___default()({\n            clientId: process.env.GOOGLE_CLIENT_ID || \"\",\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET || \"\",\n            authorization: {\n                params: {\n                    prompt: \"consent\",\n                    access_type: \"offline\",\n                    response_type: \"code\"\n                }\n            }\n        })\n    ],\n    pages: {\n        signIn: \"/\",\n        error: \"/auth/error\"\n    },\n    callbacks: {\n        async signIn ({ user, account, profile }) {\n            try {\n                console.log(\"\\uD83D\\uDD10 SignIn callback triggered:\", {\n                    user: user.email,\n                    provider: account?.provider,\n                    profile: profile?.name\n                });\n                if (account?.provider === \"google\") {\n                    console.log(\"✅ Google OAuth sign-in successful for:\", user.email);\n                    // For now, we'll skip database operations and just allow sign-in\n                    // TODO: Re-enable database operations once connection is fixed\n                    return true;\n                }\n                return true;\n            } catch (error) {\n                console.error(\"❌ Error during sign in:\", error);\n                return false;\n            }\n        },\n        async jwt ({ token, user, account, profile, trigger }) {\n            console.log(\"\\uD83D\\uDD11 JWT callback triggered:\", {\n                hasUser: !!user,\n                tokenEmail: token.email,\n                userEmail: user?.email,\n                trigger\n            });\n            if (user) {\n                console.log(\"\\uD83D\\uDC64 Setting up JWT token for new user:\", user.email);\n                // For testing, we'll set default values without database lookup\n                token.id = user.id || user.email || \"temp-id\"; // Use email as fallback ID\n                token.role = \"CLIENT\"; // Default role\n                token.status = \"ACTIVE\";\n                token.language = \"ar\";\n                token.firstName = user.name?.split(\" \")[0] || \"User\";\n                token.lastName = user.name?.split(\" \").slice(1).join(\" \") || \"\";\n                token.avatar = user.image || null;\n                token.hasCompletedOnboarding = false; // Always false for new users\n                console.log(\"✅ JWT token configured:\", {\n                    id: token.id,\n                    role: token.role,\n                    hasCompletedOnboarding: token.hasCompletedOnboarding\n                });\n            }\n            // Handle onboarding completion updates\n            if (trigger === \"update\") {\n                console.log(\"\\uD83D\\uDD04 JWT token update triggered\");\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            console.log(\"\\uD83D\\uDCCB Session callback triggered:\", {\n                tokenEmail: token.email,\n                sessionEmail: session.user?.email,\n                hasCompletedOnboarding: token.hasCompletedOnboarding\n            });\n            if (token) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n                session.user.status = token.status;\n                session.user.language = token.language;\n                session.user.firstName = token.firstName;\n                session.user.lastName = token.lastName;\n                session.user.avatar = token.avatar;\n                session.user.hasCompletedOnboarding = token.hasCompletedOnboarding;\n                console.log(\"✅ Session configured:\", {\n                    id: session.user.id,\n                    role: session.user.role,\n                    hasCompletedOnboarding: session.user.hasCompletedOnboarding\n                });\n            }\n            return session;\n        },\n        async redirect ({ url, baseUrl, token }) {\n            // Handle role-based redirects after successful authentication\n            try {\n                console.log(\"\\uD83D\\uDD04 NextAuth redirect called with:\", {\n                    url,\n                    baseUrl,\n                    hasToken: !!token\n                });\n                // Check if this is a post-authentication callback\n                if (url.includes(\"/api/auth/callback\") || url === baseUrl || url === `${baseUrl}/`) {\n                    console.log(\"\\uD83D\\uDD04 Post-authentication callback detected\");\n                    // Check if user has completed onboarding (from token)\n                    if (token?.hasCompletedOnboarding) {\n                        console.log(\"✅ User has completed onboarding, redirecting to appropriate dashboard\");\n                        // Redirect to appropriate dashboard based on role\n                        switch(token.role){\n                            case \"ADMIN\":\n                                console.log(\"\\uD83D\\uDD04 Redirecting ADMIN to admin dashboard\");\n                                return \"http://localhost:3001/dashboard\";\n                            case \"EXPERT\":\n                                console.log(\"\\uD83D\\uDD04 Redirecting EXPERT to expert dashboard\");\n                                return \"http://localhost:3002/dashboard\";\n                            case \"CLIENT\":\n                                console.log(\"\\uD83D\\uDD04 Redirecting CLIENT to landing page with success\");\n                                return `${baseUrl}/?auth=success&role=client&onboarding=complete`;\n                            default:\n                                console.log(\"\\uD83D\\uDD04 Redirecting unknown role to landing page with success\");\n                                return `${baseUrl}/?auth=success&onboarding=complete`;\n                        }\n                    } else {\n                        // User needs to complete onboarding\n                        console.log(\"\\uD83E\\uDD16 User needs onboarding - redirecting to AI onboarding\");\n                        return `${baseUrl}/ai-onboarding`;\n                    }\n                }\n                // Handle relative URLs\n                if (url.startsWith(\"/\")) {\n                    const fullUrl = `${baseUrl}${url}`;\n                    console.log(\"\\uD83D\\uDD04 Converting relative URL to absolute:\", fullUrl);\n                    return fullUrl;\n                }\n                // Handle same-origin URLs\n                if (url.startsWith(baseUrl)) {\n                    console.log(\"\\uD83D\\uDD04 Same-origin URL redirect:\", url);\n                    return url;\n                }\n                // Default fallback - redirect to landing page for safety\n                console.log(\"\\uD83C\\uDFE0 Fallback redirect to landing page\");\n                return `${baseUrl}/?auth=success`;\n            } catch (error) {\n                console.error(\"❌ Redirect error:\", error);\n                return `${baseUrl}/?auth=error`;\n            }\n        }\n    },\n    session: {\n        strategy: \"jwt\",\n        maxAge: 30 * 24 * 60 * 60\n    },\n    jwt: {\n        maxAge: 30 * 24 * 60 * 60\n    },\n    events: {\n        async signIn ({ user, account }) {\n            console.log(`🎉 User ${user.email} signed in with ${account?.provider}`);\n        // TODO: Re-enable database operations once connection is fixed\n        },\n        async signOut ({ token }) {\n            console.log(`👋 User ${token?.email} signed out`);\n        }\n    },\n    debug: \"development\" === \"development\"\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (next_auth__WEBPACK_IMPORTED_MODULE_0___default()(authOptions));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/auth/[...nextauth].ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();