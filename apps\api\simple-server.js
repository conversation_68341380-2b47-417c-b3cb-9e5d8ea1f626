require('dotenv').config();
const express = require('express');
const cors = require('cors');

console.log('Starting simple API server...');
console.log('Environment variables loaded:');
console.log('- SUPABASE_URL:', process.env.SUPABASE_URL ? 'configured' : 'not configured');
console.log('- SUPABASE_ANON_KEY:', process.env.SUPABASE_ANON_KEY ? 'configured' : 'not configured');
console.log('- OPENROUTER_API_KEY:', process.env.OPENROUTER_API_KEY ? 'configured' : 'not configured');

const app = express();
const PORT = 3001;

// Basic middleware
app.use(cors());
app.use(express.json());

// Health endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    message: 'Simple API server is running'
  });
});

// Test connection endpoint
app.get('/api/v1/test-connection', (req, res) => {
  res.json({
    success: true,
    message: 'API connection test successful',
    timestamp: new Date().toISOString(),
    supabase: {
      url: process.env.SUPABASE_URL ? 'configured' : 'not configured',
      anonKey: process.env.SUPABASE_ANON_KEY ? 'configured' : 'not configured'
    },
    openrouter: {
      apiKey: process.env.OPENROUTER_API_KEY ? 'configured' : 'not configured'
    }
  });
});

// AI test endpoint
app.get('/api/v1/ai/test-connection', (req, res) => {
  res.json({
    success: true,
    message: 'AI endpoint is accessible',
    timestamp: new Date().toISOString(),
    openrouter: {
      configured: !!process.env.OPENROUTER_API_KEY,
      status: 'ready'
    },
    supabase: {
      configured: !!(process.env.SUPABASE_URL && process.env.SUPABASE_ANON_KEY),
      status: 'ready'
    }
  });
});

// AI conversation start endpoint (mock for testing)
app.post('/api/v1/ai/conversation/start', (req, res) => {
  console.log('🤖 AI conversation start request:', req.body);

  const { userRole, language, sessionType } = req.body;

  // Generate mock session data
  const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  // Generate welcome message based on role
  const welcomeMessages = {
    EXPERT: 'مرحباً بك! أنا مساعدك الذكي في فريلا سوريا. سأساعدك في إعداد ملفك الشخصي كخبير. دعنا نبدأ بالتعرف على مهاراتك وخبراتك.',
    CLIENT: 'مرحباً بك! أنا مساعدك الذكي في فريلا سوريا. سأساعدك في تحديد احتياجاتك وإيجاد الخبراء المناسبين لمشاريعك. دعنا نبدأ!'
  };

  const welcomeMessage = welcomeMessages[userRole] || welcomeMessages.CLIENT;

  res.status(201).json({
    success: true,
    message: 'AI conversation started successfully',
    data: {
      sessionId: sessionId,
      currentStep: 'welcome',
      messages: [
        {
          id: `msg_${Date.now()}`,
          role: 'assistant',
          content: welcomeMessage,
          timestamp: new Date().toISOString(),
          confidence: 1.0
        }
      ],
      extractedData: {},
      status: 'active',
      completionRate: 0.0
    }
  });
});

// AI conversation message endpoint (mock for testing)
app.post('/api/v1/ai/conversation/message', (req, res) => {
  console.log('💬 AI conversation message request:', req.body);

  const { sessionId, message, messageType } = req.body;

  // Generate mock AI response
  const responses = [
    'شكراً لك على هذه المعلومات. يمكنك إخباري أكثر عن خبراتك في هذا المجال؟',
    'هذا رائع! ما هي أهم المشاريع التي عملت عليها؟',
    'ممتاز! دعني أسألك عن أسعارك المتوقعة لهذا النوع من الخدمات.',
    'أفهم احتياجاتك. ما هي الميزانية المتوقعة لمشروعك؟',
    'بناءً على ما ذكرته، أعتقد أنني أفهم متطلباتك بشكل أفضل الآن.'
  ];

  const randomResponse = responses[Math.floor(Math.random() * responses.length)];

  // Simple completion logic - complete after 3-5 messages
  const messageCount = Math.floor(Math.random() * 3) + 3; // 3-5 messages
  const currentCompletionRate = Math.min(0.9, Math.random() + 0.3);
  const isCompleted = currentCompletionRate > 0.8;

  res.json({
    success: true,
    message: 'Message processed successfully',
    data: {
      aiMessage: {
        id: `msg_${Date.now()}`,
        role: 'assistant',
        content: isCompleted ?
          'ممتاز! لقد جمعت كل المعلومات اللازمة. سأقوم الآن بإعداد ملفك الشخصي.' :
          randomResponse,
        timestamp: new Date().toISOString(),
        confidence: 0.9
      },
      extractedData: {
        // Mock extracted data
        skills: ['تطوير الويب', 'التصميم', 'البرمجة'],
        experience: 'متوسط',
        budget: 'متوسط',
        location: 'دمشق',
        availability: 'دوام جزئي'
      },
      isCompleted: isCompleted,
      completionRate: isCompleted ? 1.0 : currentCompletionRate,
      currentStep: isCompleted ? 'completion' : 'conversation'
    }
  });
});

// Onboarding data save endpoint (mock for testing)
app.post('/api/onboarding/user-data', (req, res) => {
  console.log('💾 Onboarding data save request:', req.body);

  const { userId, firstName, lastName, email, phoneNumber, location, role } = req.body;

  // Mock successful save
  res.json({
    success: true,
    message: 'User data saved successfully',
    data: {
      userId: userId,
      dataCollected: true,
      nextStep: 'ai_introduction'
    }
  });
});

// AI onboarding completion endpoint (mock for testing)
app.post('/api/ai/onboarding/complete', (req, res) => {
  console.log('🎉 AI onboarding completion request:', req.body);

  const { sessionId, extractedData } = req.body;

  // Mock successful completion
  res.json({
    success: true,
    message: 'Onboarding completed successfully',
    data: {
      sessionId: sessionId,
      completed: true,
      extractedData: extractedData,
      completedAt: new Date().toISOString()
    }
  });
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'Freela Syria API Server (Simple)',
    version: 'v1',
    timestamp: new Date().toISOString(),
    endpoints: {
      health: '/health',
      testConnection: '/api/v1/test-connection',
      aiTest: '/api/v1/ai/test-connection'
    }
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Simple API server running on port ${PORT}`);
  console.log(`🏥 Health: http://localhost:${PORT}/health`);
  console.log(`🔗 Test: http://localhost:${PORT}/api/v1/test-connection`);
  console.log(`🤖 AI Test: http://localhost:${PORT}/api/v1/ai/test-connection`);
});
